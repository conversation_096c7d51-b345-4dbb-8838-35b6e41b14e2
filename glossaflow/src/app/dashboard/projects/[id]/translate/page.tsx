'use client';

import { useState, useEffect } from 'react';
import { use<PERSON>ara<PERSON>, useRouter } from 'next/navigation';
import { DashboardLayout } from '@/components/layout/DashboardLayout';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Skeleton } from '@/components/ui/skeleton';
import { Alert, AlertDescription } from '@/components/ui/alert';
import {
  ChevronLeft,
  ChevronRight,
  Save,
  MessageSquare,
  BookOpen,
  Users,
  Clock,
  CheckCircle,
  AlertCircle,
  CreditCard,
  RefreshCw,
  ArrowLeft
} from 'lucide-react';
import { TranslationEditor } from '@/components/translation/TranslationEditor';
import { TerminologySidebar } from '@/components/translation/TerminologySidebar';
import { CommentsSidebar } from '@/components/translation/CommentsSidebar';
import { ChapterList } from '@/components/chapters/ChapterList';
import { ChapterNavigation } from '@/components/chapters/ChapterNavigation';
import { ChapterProgress } from '@/components/chapters/ChapterProgress';
import { CreditBalance } from '@/components/credits/CreditBalance';
import { CreditRequirement } from '@/components/credits/CreditRequirement';
import { AITranslationButton } from '@/components/translation/AITranslationButton';
import { useGetProjectQuery } from '@/lib/api/projects';
import { useGetChaptersQuery, useGetChapterQuery, useGetChapterSegmentsQuery } from '@/lib/api/chapters';
import { useGetCreditsQuery, useCheckCreditSufficiencyQuery } from '@/lib/api/credits';
import { useUpdateSegmentMutation } from '@/lib/api/translation';
import type { Chapter, TranslationSegment } from '@/types';

export default function TranslatePage() {
  const params = useParams();
  const router = useRouter();
  const projectId = params.id as string;

  // State management
  const [selectedChapterId, setSelectedChapterId] = useState<string | null>(null);
  const [currentSegmentIndex, setCurrentSegmentIndex] = useState(0);
  const [showChapterList, setShowChapterList] = useState(true);
  const [showTerminology, setShowTerminology] = useState(true);
  const [showComments, setShowComments] = useState(false);
  const [isAutoSaving, setIsAutoSaving] = useState(false);
  const [targetLanguage, setTargetLanguage] = useState<string>('');

  // API queries
  const {
    data: projectResponse,
    isLoading: isProjectLoading,
    isError: isProjectError,
    refetch: refetchProject,
  } = useGetProjectQuery(projectId);

  const {
    data: chaptersResponse,
    isLoading: isChaptersLoading,
    isError: isChaptersError,
    refetch: refetchChapters,
  } = useGetChaptersQuery({
    projectId,
    sortBy: 'chapter_number',
    sortOrder: 'asc',
  });

  const {
    data: chapterResponse,
    isLoading: isChapterLoading,
    isError: isChapterError,
    refetch: refetchChapter,
  } = useGetChapterQuery(selectedChapterId!, {
    skip: !selectedChapterId,
  });

  const {
    data: segmentsResponse,
    isLoading: isSegmentsLoading,
    isError: isSegmentsError,
    refetch: refetchSegments,
  } = useGetChapterSegmentsQuery({
    chapterId: selectedChapterId!,
    limit: 100,
  }, {
    skip: !selectedChapterId,
  });

  const {
    data: creditsResponse,
    isLoading: isCreditsLoading,
  } = useGetCreditsQuery();

  // Mutations
  const [updateSegment] = useUpdateSegmentMutation();
  // Extract data from API responses
  const project = projectResponse?.data?.project;
  const chapters = chaptersResponse?.data?.items || [];
  const currentChapter = chapterResponse?.data?.chapter;
  const segments = segmentsResponse?.data?.items || [];
  const credits = creditsResponse?.data?.credits;

  // Set target language from project data
  useEffect(() => {
    if (project?.target_languages && project.target_languages.length > 0) {
      setTargetLanguage(project.target_languages[0]);
    }
  }, [project]);

  // Auto-select first chapter if none selected
  useEffect(() => {
    if (chapters.length > 0 && !selectedChapterId) {
      setSelectedChapterId(chapters[0].id);
    }
  }, [chapters, selectedChapterId]);

  // Reset segment index when chapter changes
  useEffect(() => {
    setCurrentSegmentIndex(0);
  }, [selectedChapterId]);

  // Get current segment
  const currentSegment = segments[currentSegmentIndex];

  // Check if we have sufficient credits for AI translation
  const estimatedCost = currentSegment?.word_count ? currentSegment.word_count * 0.01 : 0;
  const {
    data: creditCheckResponse,
  } = useCheckCreditSufficiencyQuery({
    requiredAmount: estimatedCost,
  }, {
    skip: !currentSegment || estimatedCost === 0,
  });

  // Status colors for badges
  const statusColors = {
    pending: 'bg-gray-100 text-gray-800',
    in_progress: 'bg-blue-100 text-blue-800',
    translated: 'bg-green-100 text-green-800',
    reviewed: 'bg-purple-100 text-purple-800',
    approved: 'bg-emerald-100 text-emerald-800',
    rejected: 'bg-red-100 text-red-800',
  };

  // Handler functions
  const handlePrevious = () => {
    if (currentSegmentIndex > 0) {
      setCurrentSegmentIndex(currentSegmentIndex - 1);
    }
  };

  const handleNext = () => {
    if (currentSegmentIndex < segments.length - 1) {
      setCurrentSegmentIndex(currentSegmentIndex + 1);
    }
  };

  const handleSave = async () => {
    if (!currentSegment) return;

    setIsAutoSaving(true);
    try {
      await updateSegment({
        id: currentSegment.id,
        data: {
          target_text: currentSegment.target_text,
          status: 'translated',
          translation_completed_at: new Date().toISOString(),
        },
      }).unwrap();

      // Refetch segments to get updated data
      refetchSegments();
    } catch (error) {
      console.error('Failed to save segment:', error);
    } finally {
      setIsAutoSaving(false);
    }
  };

  const handleChapterSelect = (chapter: Chapter) => {
    setSelectedChapterId(chapter.id);
  };

  const handleTargetTextChange = (text: string) => {
    // Update the current segment's target text locally
    // This would typically be handled by the TranslationEditor component
  };

  // Calculate progress
  const progress = currentChapter ? currentChapter.progress_percentage : 0;

  // Loading states
  if (isProjectLoading || isChaptersLoading) {
    return (
      <DashboardLayout>
        <div className="p-6 space-y-6">
          <Skeleton className="h-8 w-64" />
          <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
            <div className="lg:col-span-1">
              <Skeleton className="h-96" />
            </div>
            <div className="lg:col-span-3">
              <Skeleton className="h-96" />
            </div>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  // Error states
  if (isProjectError || isChaptersError) {
    return (
      <DashboardLayout>
        <div className="p-6">
          <Alert>
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              Failed to load project data.
              <Button
                variant="link"
                className="p-0 ml-2"
                onClick={() => {
                  refetchProject();
                  refetchChapters();
                }}
              >
                Try again
              </Button>
            </AlertDescription>
          </Alert>
        </div>
      </DashboardLayout>
    );
  }

  // No project found
  if (!project) {
    return (
      <DashboardLayout>
        <div className="p-6 text-center">
          <AlertCircle className="mx-auto h-12 w-12 text-gray-400 mb-4" />
          <h3 className="text-lg font-semibold text-gray-900 mb-2">
            Project not found
          </h3>
          <p className="text-gray-600 mb-4">
            The project you're looking for doesn't exist or you don't have access to it.
          </p>
          <Button onClick={() => router.push('/dashboard/projects')}>
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Projects
          </Button>
        </div>
      </DashboardLayout>
    );
  }

  // No chapters found
  if (!isChaptersLoading && chapters.length === 0) {
    return (
      <DashboardLayout>
        <div className="p-6 text-center">
          <BookOpen className="mx-auto h-12 w-12 text-gray-400 mb-4" />
          <h3 className="text-lg font-semibold text-gray-900 mb-2">
            No chapters found
          </h3>
          <p className="text-gray-600 mb-4">
            This project doesn't have any chapters yet. Create chapters to start translating.
          </p>
          <div className="flex items-center justify-center space-x-3">
            <Button onClick={() => router.push(`/dashboard/projects/${projectId}`)}>
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Project
            </Button>
            <Button variant="outline">
              Create Chapter
            </Button>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <div className="h-[calc(100vh-8rem)] flex flex-col">
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b bg-white">
          <div className="flex items-center space-x-4">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => router.push(`/dashboard/projects/${projectId}`)}
            >
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Project
            </Button>
            <div>
              <h1 className="text-xl font-semibold">{project.name}</h1>
              <p className="text-sm text-gray-600">
                {project.source_language} → {targetLanguage}
              </p>
            </div>
            {currentChapter && (
              <div className="flex items-center space-x-2">
                <Progress value={progress} className="w-32" />
                <span className="text-sm text-gray-600">
                  {Math.round(progress)}%
                </span>
              </div>
            )}
          </div>
          
          <div className="flex items-center space-x-2">
            {/* Credit Balance */}
            <CreditBalance
              compact={true}
              showTopUp={true}
              showUsageStats={false}
              className="w-48"
            />

            <Button
              variant={showChapterList ? 'default' : 'outline'}
              size="sm"
              onClick={() => setShowChapterList(!showChapterList)}
            >
              <BookOpen className="mr-2 h-4 w-4" />
              Chapters
            </Button>
            <Button
              variant="outline"
              size="sm"
              disabled
              className="opacity-50"
            >
              <Users className="mr-2 h-4 w-4" />
              Terminology (Always On)
            </Button>
            <Button
              variant={showComments ? 'default' : 'outline'}
              size="sm"
              onClick={() => setShowComments(!showComments)}
            >
              <MessageSquare className="mr-2 h-4 w-4" />
              Comments
            </Button>
          </div>
        </div>

        <div className="flex-1 flex">
          {/* Terminology Sidebar - Always Visible */}
          <div className="w-80 border-r bg-white flex flex-col">
            <div className="p-4 border-b bg-blue-50">
              <div className="flex items-center justify-between mb-2">
                <h3 className="font-semibold text-blue-900 flex items-center">
                  <BookOpen className="mr-2 h-5 w-5" />
                  Terminology
                </h3>
                <Badge variant="secondary" className="text-xs">
                  {targetLanguage}
                </Badge>
              </div>
              <p className="text-sm text-blue-700">
                Approved terms for consistent translation
              </p>
            </div>
            <div className="flex-1 overflow-hidden">
              <TerminologySidebar
                projectId={projectId}
                sourceText={currentSegment?.source_text || ''}
                targetLanguage={targetLanguage}
                onTermSelect={(term) => {
                  console.log('Term selected:', term);
                  // TODO: Apply term to current translation
                }}
                onAddTerm={() => {
                  console.log('Add new term');
                  // TODO: Open add term dialog
                }}
              />
            </div>
          </div>

          {/* Chapter List Sidebar */}
          {showChapterList && (
            <div className="w-80 border-r bg-gray-50 overflow-y-auto">
              <div className="p-4">
                <div className="flex items-center justify-between mb-4">
                  <h2 className="text-lg font-semibold">Chapters</h2>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setShowChapterList(false)}
                  >
                    ×
                  </Button>
                </div>
                <ChapterList
                  projectId={projectId}
                  selectedChapterId={selectedChapterId}
                  onChapterSelect={handleChapterSelect}
                  showActions={false}
                />
              </div>
            </div>
          )}

          {/* Main Translation Area */}
          <div className="flex-1 flex flex-col">
            {/* Chapter Navigation */}
            {currentChapter && (
              <ChapterNavigation
                projectId={projectId}
                currentChapter={currentChapter}
                onChapterChange={handleChapterSelect}
                showProgress={true}
                compact={false}
              />
            )}
            {/* Segment Navigation */}
            <div className="flex items-center justify-between p-4 border-b bg-gray-50">
              <div className="flex items-center space-x-4">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handlePrevious}
                  disabled={currentSegmentIndex === 0}
                >
                  <ChevronLeft className="h-4 w-4" />
                  Previous
                </Button>
                <span className="text-sm font-medium">
                  Segment {currentSegmentIndex + 1} of {segments.length}
                </span>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleNext}
                  disabled={currentSegmentIndex === segments.length - 1}
                >
                  Next
                  <ChevronRight className="h-4 w-4" />
                </Button>
              </div>

              <div className="flex items-center space-x-2">
                {currentSegment && (
                  <Badge className={statusColors[currentSegment.status as keyof typeof statusColors]}>
                    {currentSegment.status.replace('_', ' ')}
                  </Badge>
                )}
                {currentSegment?.updated_at && (
                  <span className="text-xs text-gray-500">
                    Last saved: {new Date(currentSegment.updated_at).toLocaleTimeString()}
                  </span>
                )}

                {/* AI Translation Button */}
                {currentSegment && credits && (
                  <AITranslationButton
                    segment={currentSegment}
                    sourceLanguage={project.source_language}
                    targetLanguage={targetLanguage}
                    projectId={projectId}
                    chapterId={selectedChapterId || undefined}
                    currentBalance={credits.balance}
                    onTranslationGenerated={(translation) => {
                      // Update the segment with AI translation
                      handleTargetTextChange(translation);
                    }}
                    onCreditUpdate={() => {
                      // Refetch credits
                      window.location.reload();
                    }}
                  />
                )}

                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleSave}
                  disabled={isAutoSaving}
                >
                  {isAutoSaving ? (
                    <>
                      <Clock className="mr-2 h-4 w-4 animate-spin" />
                      Saving...
                    </>
                  ) : (
                    <>
                      <Save className="mr-2 h-4 w-4" />
                      Save
                    </>
                  )}
                </Button>
              </div>
            </div>

            {/* Translation Editor */}
            <div className="flex-1 p-4 space-y-4">
              {/* Credit Requirement for Current Segment */}
              {currentSegment && credits && (
                <CreditRequirement
                  segment={currentSegment}
                  sourceText={currentSegment.source_text}
                  sourceLanguage={project.source_language}
                  targetLanguage={targetLanguage}
                  currentBalance={credits.balance}
                  onCreditUpdate={() => {
                    // Refetch credits after top-up
                    window.location.reload(); // Simple refresh for now
                  }}
                  className="max-w-md"
                />
              )}

              {currentSegment ? (
                <TranslationEditor
                  sourceText={currentSegment.source_text}
                  targetText={currentSegment.target_text || ''}
                  sourceLanguage={project.source_language}
                  targetLanguage={targetLanguage}
                  context={`Chapter ${currentChapter?.chapter_number}: ${currentChapter?.title}`}
                  domain={project.document_type || 'general'}
                  projectId={projectId}
                  onTargetTextChange={handleTargetTextChange}
                  onSave={handleSave}
                  onTermClick={(term) => {
                    console.log('Term clicked in editor:', term);
                    // TODO: Show term details or focus terminology sidebar
                  }}
                />
              ) : (
                <div className="flex items-center justify-center h-full text-gray-500">
                  {isSegmentsLoading ? (
                    <div className="text-center">
                      <RefreshCw className="mx-auto h-8 w-8 animate-spin mb-2" />
                      <p>Loading segments...</p>
                    </div>
                  ) : (
                    <div className="text-center">
                      <BookOpen className="mx-auto h-8 w-8 mb-2" />
                      <p>No segments available for this chapter</p>
                    </div>
                  )}
                </div>
              )}
            </div>

            {/* Segment List */}
            <div className="h-48 border-t bg-gray-50 overflow-y-auto">
              <div className="p-4">
                <h3 className="text-sm font-medium mb-3">
                  Chapter Segments ({segments.length})
                </h3>
                {isSegmentsLoading ? (
                  <div className="space-y-2">
                    {Array.from({ length: 3 }).map((_, i) => (
                      <div key={i} className="p-3 bg-white rounded-lg">
                        <div className="flex items-center justify-between mb-2">
                          <Skeleton className="h-4 w-20" />
                          <Skeleton className="h-5 w-16" />
                        </div>
                        <Skeleton className="h-3 w-full" />
                      </div>
                    ))}
                  </div>
                ) : segments.length > 0 ? (
                  <div className="space-y-2">
                    {segments.map((segment, index) => (
                      <div
                        key={segment.id}
                        className={`p-3 rounded-lg cursor-pointer transition-colors border ${
                          index === currentSegmentIndex
                            ? 'bg-blue-100 border-blue-200'
                            : 'bg-white hover:bg-gray-50 border-gray-200'
                        }`}
                        onClick={() => setCurrentSegmentIndex(index)}
                      >
                        <div className="flex items-center justify-between mb-1">
                          <span className="text-sm font-medium">
                            Segment {segment.segment_number}
                          </span>
                          <div className="flex items-center space-x-2">
                            <Badge
                              variant="outline"
                              className={statusColors[segment.status as keyof typeof statusColors]}
                            >
                              {segment.status.replace('_', ' ')}
                            </Badge>
                            {segment.word_count && (
                              <span className="text-xs text-gray-500">
                                {segment.word_count} words
                              </span>
                            )}
                          </div>
                        </div>
                        <p className="text-xs text-gray-600 truncate">
                          {segment.source_text}
                        </p>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-8 text-gray-500">
                    <BookOpen className="mx-auto h-8 w-8 mb-2" />
                    <p className="text-sm">No segments in this chapter</p>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Additional Sidebars */}

          {showComments && currentSegment && (
            <div className="w-80 border-l">
              <CommentsSidebar
                segmentId={currentSegment.id}
                comments={[]}
                onAddComment={(comment) => {
                  console.log('Add comment:', comment);
                }}
              />
            </div>
          )}
        </div>
      </div>
    </DashboardLayout>
  );
}
