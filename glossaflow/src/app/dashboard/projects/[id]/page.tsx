'use client';

import { useState } from 'react';
import { use<PERSON><PERSON><PERSON>, useRouter } from 'next/navigation';
import { DashboardLayout } from '@/components/layout/DashboardLayout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { 
  Play, 
  Pause, 
  Settings, 
  Users, 
  FileText, 
  MessageSquare,
  Calendar,
  Globe,
  BarChart3,
  Download,
  Upload,
  Edit,
  Trash2
} from 'lucide-react';

// Mock project data
const mockProject = {
  id: '1',
  name: 'Mobile App Localization',
  description: 'Complete localization of our mobile application for Japanese market including UI strings, help documentation, and marketing materials.',
  status: 'in_progress',
  sourceLanguage: 'English',
  targetLanguages: ['Japanese', 'Korean'],
  createdAt: '2024-01-10',
  dueDate: '2024-02-15',
  priority: 'high',
  totalSegments: 1250,
  completedSegments: 938,
  reviewedSegments: 756,
  approvedSegments: 623,
  budget: '$15,000',
  spent: '$8,750',
  teamMembers: [
    {
      id: '1',
      name: 'Sarah Chen',
      role: 'Lead Translator',
      avatar: '',
      language: 'Japanese',
      segmentsCompleted: 456,
    },
    {
      id: '2',
      name: 'Mike Johnson',
      role: 'Reviewer',
      avatar: '',
      language: 'Japanese',
      segmentsReviewed: 234,
    },
    {
      id: '3',
      name: 'Emma Davis',
      role: 'Project Manager',
      avatar: '',
      language: 'All',
      segmentsCompleted: 0,
    },
    {
      id: '4',
      name: 'Alex Kim',
      role: 'Translator',
      avatar: '',
      language: 'Korean',
      segmentsCompleted: 312,
    },
  ],
  files: [
    {
      id: '1',
      name: 'app_strings.json',
      size: '45 KB',
      type: 'JSON',
      uploadedAt: '2024-01-10',
      segments: 234,
    },
    {
      id: '2',
      name: 'help_documentation.md',
      size: '128 KB',
      type: 'Markdown',
      uploadedAt: '2024-01-10',
      segments: 567,
    },
    {
      id: '3',
      name: 'marketing_copy.docx',
      size: '89 KB',
      type: 'Word Document',
      uploadedAt: '2024-01-12',
      segments: 449,
    },
  ],
  recentActivity: [
    {
      id: '1',
      user: 'Sarah Chen',
      action: 'completed translation for',
      target: 'app_strings.json',
      timestamp: '2 hours ago',
    },
    {
      id: '2',
      user: 'Mike Johnson',
      action: 'reviewed',
      target: '45 segments',
      timestamp: '4 hours ago',
    },
    {
      id: '3',
      user: 'Alex Kim',
      action: 'started translation for',
      target: 'marketing_copy.docx',
      timestamp: '6 hours ago',
    },
  ],
};

const statusColors = {
  draft: 'bg-gray-100 text-gray-800',
  in_progress: 'bg-blue-100 text-blue-800',
  review: 'bg-yellow-100 text-yellow-800',
  completed: 'bg-green-100 text-green-800',
  on_hold: 'bg-red-100 text-red-800',
};

const priorityColors = {
  low: 'bg-green-100 text-green-800',
  medium: 'bg-yellow-100 text-yellow-800',
  high: 'bg-orange-100 text-orange-800',
  urgent: 'bg-red-100 text-red-800',
};

export default function ProjectDetailPage() {
  const params = useParams();
  const router = useRouter();
  const projectId = params.id as string;
  
  const [project] = useState(mockProject);
  
  const progress = (project.completedSegments / project.totalSegments) * 100;
  const reviewProgress = (project.reviewedSegments / project.totalSegments) * 100;
  const approvalProgress = (project.approvedSegments / project.totalSegments) * 100;
  const budgetProgress = (parseFloat(project.spent.replace('$', '').replace(',', '')) / 
                         parseFloat(project.budget.replace('$', '').replace(',', ''))) * 100;

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
          <div>
            <div className="flex items-center space-x-3 mb-2">
              <h1 className="text-2xl font-bold text-gray-900">{project.name}</h1>
              <Badge className={statusColors[project.status as keyof typeof statusColors]}>
                {project.status.replace('_', ' ')}
              </Badge>
              <Badge className={priorityColors[project.priority as keyof typeof priorityColors]}>
                {project.priority} priority
              </Badge>
            </div>
            <p className="text-gray-600">{project.description}</p>
          </div>
          <div className="flex space-x-2 mt-4 sm:mt-0">
            <Button variant="outline">
              <Settings className="mr-2 h-4 w-4" />
              Settings
            </Button>
            <Button onClick={() => router.push(`/dashboard/projects/${projectId}/translate`)}>
              <Play className="mr-2 h-4 w-4" />
              Start Translation
            </Button>
          </div>
        </div>

        {/* Overview Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <div className="p-2 bg-blue-100 rounded-lg">
                  <FileText className="h-6 w-6 text-blue-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Total Segments</p>
                  <p className="text-2xl font-bold text-gray-900">{project.totalSegments}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <div className="p-2 bg-green-100 rounded-lg">
                  <BarChart3 className="h-6 w-6 text-green-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Progress</p>
                  <p className="text-2xl font-bold text-gray-900">{Math.round(progress)}%</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <div className="p-2 bg-purple-100 rounded-lg">
                  <Users className="h-6 w-6 text-purple-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Team Members</p>
                  <p className="text-2xl font-bold text-gray-900">{project.teamMembers.length}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <div className="p-2 bg-orange-100 rounded-lg">
                  <Calendar className="h-6 w-6 text-orange-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Due Date</p>
                  <p className="text-lg font-bold text-gray-900">{project.dueDate}</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Progress Section */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <Card>
            <CardHeader>
              <CardTitle>Translation Progress</CardTitle>
              <CardDescription>Track completion across different stages</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <div className="flex justify-between text-sm mb-2">
                  <span>Translation</span>
                  <span>{project.completedSegments}/{project.totalSegments} segments</span>
                </div>
                <Progress value={progress} />
              </div>
              
              <div>
                <div className="flex justify-between text-sm mb-2">
                  <span>Review</span>
                  <span>{project.reviewedSegments}/{project.totalSegments} segments</span>
                </div>
                <Progress value={reviewProgress} />
              </div>
              
              <div>
                <div className="flex justify-between text-sm mb-2">
                  <span>Approval</span>
                  <span>{project.approvedSegments}/{project.totalSegments} segments</span>
                </div>
                <Progress value={approvalProgress} />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Project Details</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <p className="text-sm font-medium text-gray-600">Source Language</p>
                  <p className="text-sm">{project.sourceLanguage}</p>
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-600">Target Languages</p>
                  <div className="flex flex-wrap gap-1 mt-1">
                    {project.targetLanguages.map((lang) => (
                      <Badge key={lang} variant="outline" size="sm">
                        {lang}
                      </Badge>
                    ))}
                  </div>
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-600">Created</p>
                  <p className="text-sm">{project.createdAt}</p>
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-600">Budget</p>
                  <p className="text-sm">{project.spent} / {project.budget}</p>
                  <Progress value={budgetProgress} className="mt-1" />
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Team and Files Section */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Team Members */}
          <Card>
            <CardHeader className="flex flex-row items-center justify-between">
              <div>
                <CardTitle>Team Members</CardTitle>
                <CardDescription>Project contributors and their roles</CardDescription>
              </div>
              <Button variant="outline" size="sm">
                <Users className="mr-2 h-4 w-4" />
                Manage Team
              </Button>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {project.teamMembers.map((member) => (
                  <div key={member.id} className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <Avatar className="h-10 w-10">
                        <AvatarImage src={member.avatar} />
                        <AvatarFallback>
                          {member.name.split(' ').map(n => n[0]).join('')}
                        </AvatarFallback>
                      </Avatar>
                      <div>
                        <p className="text-sm font-medium">{member.name}</p>
                        <p className="text-xs text-gray-500">{member.role} • {member.language}</p>
                      </div>
                    </div>
                    <div className="text-right">
                      <p className="text-sm font-medium">
                        {member.segmentsCompleted || member.segmentsReviewed || 0}
                      </p>
                      <p className="text-xs text-gray-500">
                        {member.role.includes('Reviewer') ? 'reviewed' : 'completed'}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Project Files */}
          <Card>
            <CardHeader className="flex flex-row items-center justify-between">
              <div>
                <CardTitle>Project Files</CardTitle>
                <CardDescription>Source documents and assets</CardDescription>
              </div>
              <Button variant="outline" size="sm">
                <Upload className="mr-2 h-4 w-4" />
                Upload
              </Button>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {project.files.map((file) => (
                  <div key={file.id} className="flex items-center justify-between p-3 border rounded-lg">
                    <div className="flex items-center space-x-3">
                      <div className="p-2 bg-gray-100 rounded">
                        <FileText className="h-4 w-4 text-gray-600" />
                      </div>
                      <div>
                        <p className="text-sm font-medium">{file.name}</p>
                        <p className="text-xs text-gray-500">
                          {file.size} • {file.segments} segments • {file.uploadedAt}
                        </p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Badge variant="outline" size="sm">
                        {file.type}
                      </Badge>
                      <Button variant="ghost" size="sm">
                        <Download className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Recent Activity */}
        <Card>
          <CardHeader>
            <CardTitle>Recent Activity</CardTitle>
            <CardDescription>Latest updates and changes to the project</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {project.recentActivity.map((activity) => (
                <div key={activity.id} className="flex items-center space-x-3">
                  <div className="p-1 bg-blue-100 rounded-full">
                    <MessageSquare className="h-4 w-4 text-blue-600" />
                  </div>
                  <div className="flex-1">
                    <p className="text-sm">
                      <span className="font-medium">{activity.user}</span>{' '}
                      {activity.action}{' '}
                      <span className="font-medium">{activity.target}</span>
                    </p>
                    <p className="text-xs text-gray-500">{activity.timestamp}</p>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  );
}
