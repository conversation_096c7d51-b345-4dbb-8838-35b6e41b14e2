import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { createGeminiService } from '@/lib/services/gemini-ai';

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user?.email) {
      return NextResponse.json(
        { error: 'Unauthorized', success: false },
        { status: 401 }
      );
    }

    const body = await request.json();
    const {
      sourceText,
      sourceLanguage,
      targetLanguage,
      context,
      domain,
      projectId,
    } = body;

    // Validate required fields
    if (!sourceText || !sourceLanguage || !targetLanguage) {
      return NextResponse.json(
        { error: 'Missing required fields: sourceText, sourceLanguage, targetLanguage', success: false },
        { status: 400 }
      );
    }

    // Validate text length
    if (sourceText.length > 5000) {
      return NextResponse.json(
        { error: 'Source text too long (max 5000 characters)', success: false },
        { status: 400 }
      );
    }

    try {
      // Create Gemini service instance
      const geminiService = createGeminiService();
      
      // Get AI translation suggestion
      const suggestion = await geminiService.getTranslationSuggestions(
        sourceText,
        sourceLanguage,
        targetLanguage,
        context
      );

      // Enhance with domain-specific context if provided
      let enhancedSuggestion = suggestion;
      if (domain) {
        enhancedSuggestion = {
          ...suggestion,
          explanation: suggestion.explanation 
            ? `${suggestion.explanation} (Domain: ${domain})`
            : `Translation optimized for ${domain} domain.`
        };
      }

      return NextResponse.json({
        success: true,
        data: enhancedSuggestion,
        message: 'Translation suggestion generated successfully',
      });

    } catch (aiError) {
      console.error('AI translation error:', aiError);
      
      // Fallback to basic response if AI fails
      return NextResponse.json({
        success: true,
        data: {
          sourceText,
          targetText: `[AI translation unavailable for: ${sourceText}]`,
          confidence: 0.1,
          alternatives: [],
          explanation: 'AI service temporarily unavailable. Please translate manually.',
        },
        message: 'AI service unavailable, manual translation required',
      });
    }

  } catch (error) {
    console.error('API error:', error);
    return NextResponse.json(
      { 
        error: 'Internal server error', 
        success: false,
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

// GET endpoint for testing AI service status
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user?.email) {
      return NextResponse.json(
        { error: 'Unauthorized', success: false },
        { status: 401 }
      );
    }

    // Check if Gemini API key is configured
    const hasApiKey = !!process.env.GEMINI_API_KEY;
    
    if (!hasApiKey) {
      return NextResponse.json({
        success: false,
        data: {
          available: false,
          reason: 'Gemini API key not configured',
        },
        message: 'AI translation service not available',
      });
    }

    // Test basic AI service connectivity
    try {
      const geminiService = createGeminiService();
      
      // Simple test translation
      const testResult = await geminiService.getTranslationSuggestions(
        'Hello',
        'en',
        'es'
      );

      return NextResponse.json({
        success: true,
        data: {
          available: true,
          testResult: {
            sourceText: testResult.sourceText,
            targetText: testResult.targetText,
            confidence: testResult.confidence,
          },
        },
        message: 'AI translation service is available and working',
      });

    } catch (testError) {
      console.error('AI service test failed:', testError);
      
      return NextResponse.json({
        success: false,
        data: {
          available: false,
          reason: 'AI service connectivity test failed',
          error: testError instanceof Error ? testError.message : 'Unknown error',
        },
        message: 'AI translation service test failed',
      });
    }

  } catch (error) {
    console.error('API error:', error);
    return NextResponse.json(
      { 
        error: 'Internal server error', 
        success: false,
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
