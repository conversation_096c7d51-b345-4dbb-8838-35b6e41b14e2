import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import { createClient } from '@supabase/supabase-js';

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user?.email) {
      return NextResponse.json(
        { error: 'Unauthorized', success: false },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const search = searchParams.get('search');
    const category = searchParams.get('category');
    const targetLanguage = searchParams.get('targetLanguage');
    const approvalStatus = searchParams.get('approvalStatus');
    const projectId = searchParams.get('projectId');

    const offset = (page - 1) * limit;

    // Build the query
    let query = supabase
      .from('terminology_entries')
      .select(`
        *,
        created_by_user:users!terminology_entries_created_by_fkey(
          id,
          name,
          email
        ),
        reviewed_by_user:users!terminology_entries_reviewed_by_fkey(
          id,
          name,
          email
        )
      `);

    // Apply filters
    if (search) {
      query = query.or(`source_term.ilike.%${search}%,target_term.ilike.%${search}%`);
    }
    if (category && category !== 'all') {
      query = query.eq('category', category);
    }
    if (targetLanguage && targetLanguage !== 'all') {
      query = query.eq('target_language', targetLanguage);
    }
    if (approvalStatus && approvalStatus !== 'all') {
      query = query.eq('approval_status', approvalStatus);
    }
    if (projectId) {
      query = query.eq('project_id', projectId);
    }

    // Get total count
    const { count } = await supabase
      .from('terminology_entries')
      .select('*', { count: 'exact', head: true });

    // Get paginated results
    const { data: terminology, error } = await query
      .range(offset, offset + limit - 1)
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Database error:', error);
      return NextResponse.json(
        { error: 'Failed to fetch terminology', success: false },
        { status: 500 }
      );
    }

    // Transform the data to match the expected format
    const transformedTerminology = terminology?.map(entry => ({
      id: entry.id,
      sourceTerm: entry.source_term,
      targetTerm: entry.target_term,
      targetLanguage: entry.target_language,
      category: entry.category,
      context: entry.context,
      usageNotes: entry.usage_notes,
      approvalStatus: entry.approval_status,
      frequency: entry.frequency || 0,
      createdBy: entry.created_by,
      reviewedBy: entry.reviewed_by,
      lastUsed: entry.last_used,
      createdAt: entry.created_at,
      updatedAt: entry.updated_at,
      organizationId: entry.organization_id,
      projectId: entry.project_id,
    })) || [];

    const totalPages = Math.ceil((count || 0) / limit);

    return NextResponse.json({
      success: true,
      data: {
        items: transformedTerminology,
        total: count || 0,
        page,
        limit,
        totalPages,
      },
    });
  } catch (error) {
    console.error('API error:', error);
    return NextResponse.json(
      { error: 'Internal server error', success: false },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user?.email) {
      return NextResponse.json(
        { error: 'Unauthorized', success: false },
        { status: 401 }
      );
    }

    const body = await request.json();
    const {
      sourceTerm,
      targetTerm,
      targetLanguage,
      category,
      context,
      usageNotes,
      projectId,
    } = body;

    // Validate required fields
    if (!sourceTerm || !targetTerm || !targetLanguage || !category) {
      return NextResponse.json(
        { error: 'Missing required fields', success: false },
        { status: 400 }
      );
    }

    // Create the terminology entry
    const { data: entry, error: entryError } = await supabase
      .from('terminology_entries')
      .insert({
        source_term: sourceTerm,
        target_term: targetTerm,
        target_language: targetLanguage,
        category,
        context,
        usage_notes: usageNotes,
        project_id: projectId,
        created_by: session.user.id,
        organization_id: session.user.organizationId || null,
        approval_status: 'pending',
        frequency: 0,
      })
      .select()
      .single();

    if (entryError) {
      console.error('Terminology creation error:', entryError);
      return NextResponse.json(
        { error: 'Failed to create terminology entry', success: false },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      data: {
        id: entry.id,
        sourceTerm: entry.source_term,
        targetTerm: entry.target_term,
        targetLanguage: entry.target_language,
        category: entry.category,
        context: entry.context,
        usageNotes: entry.usage_notes,
        approvalStatus: entry.approval_status,
        frequency: entry.frequency || 0,
        createdBy: entry.created_by,
        reviewedBy: entry.reviewed_by,
        lastUsed: entry.last_used,
        createdAt: entry.created_at,
        updatedAt: entry.updated_at,
        organizationId: entry.organization_id,
        projectId: entry.project_id,
      },
      message: 'Terminology entry created successfully',
    });
  } catch (error) {
    console.error('API error:', error);
    return NextResponse.json(
      { error: 'Internal server error', success: false },
      { status: 500 }
    );
  }
}
