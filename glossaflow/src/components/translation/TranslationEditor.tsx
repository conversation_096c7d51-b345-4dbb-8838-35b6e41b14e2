'use client';

import { useState, useEffect, useRef } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Textarea } from '@/components/ui/textarea';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  Copy,
  RotateCcw,
  CheckCircle,
  AlertTriangle,
  Lightbulb,
  Sparkles
} from 'lucide-react';
import { AITranslationSuggestions } from './AITranslationSuggestions';
import { TerminologyHighlighter } from './TerminologyHighlighter';
import { SmartTranslationTextarea } from './SmartTranslationTextarea';
import { TerminologyValidationPanel } from './TerminologyValidationPanel';
import { TerminologyWarningBanner } from './TerminologyWarningBanner';
import { TerminologyQuickActions } from './TerminologyQuickActions';
import { SelectableText } from './SelectableText';

interface TranslationEditorProps {
  sourceText: string;
  targetText: string;
  sourceLanguage?: string;
  targetLanguage?: string;
  context?: string;
  domain?: string;
  projectId?: string;
  onTargetTextChange: (text: string) => void;
  onSave: () => void;
  onTermClick?: (term: any) => void;
}

// Mock translation memory matches
const mockTMMatches = [
  {
    id: '1',
    sourceText: 'Welcome to our mobile application.',
    targetText: '私たちのモバイルアプリケーションへようこそ。',
    similarity: 95,
    origin: 'Previous Project',
  },
  {
    id: '2',
    sourceText: 'Welcome to our platform.',
    targetText: '私たちのプラットフォームへようこそ。',
    similarity: 78,
    origin: 'Translation Memory',
  },
];

// Mock terminology suggestions
const mockTermSuggestions = [
  {
    sourceTerm: 'mobile application',
    targetTerm: 'モバイルアプリケーション',
    category: 'technical',
  },
  {
    sourceTerm: 'tasks',
    targetTerm: 'タスク',
    category: 'general',
  },
];

export function TranslationEditor({
  sourceText,
  targetText,
  sourceLanguage = 'en',
  targetLanguage = 'ja',
  context,
  domain,
  projectId,
  onTargetTextChange,
  onSave,
  onTermClick
}: TranslationEditorProps) {
  const [currentTargetText, setCurrentTargetText] = useState(targetText);
  const [wordCount, setWordCount] = useState(0);
  const [charCount, setCharCount] = useState(0);
  const [hasChanges, setHasChanges] = useState(false);
  const [selectedText, setSelectedText] = useState('');
  const [suggestedTranslation, setSuggestedTranslation] = useState('');
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  useEffect(() => {
    setCurrentTargetText(targetText);
  }, [targetText]);

  useEffect(() => {
    const words = currentTargetText.trim().split(/\s+/).filter(word => word.length > 0);
    setWordCount(words.length);
    setCharCount(currentTargetText.length);
    setHasChanges(currentTargetText !== targetText);
  }, [currentTargetText, targetText]);

  const handleTextChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const newText = e.target.value;
    setCurrentTargetText(newText);
    onTargetTextChange(newText);
  };

  const handleCopySource = () => {
    setCurrentTargetText(sourceText);
    onTargetTextChange(sourceText);
  };

  const handleApplyTerm = (sourceTerm: string, targetTerm: string) => {
    // Replace the source term with target term in the current translation
    const newText = currentTargetText.replace(
      new RegExp(`\\b${sourceTerm}\\b`, 'gi'),
      targetTerm
    );
    setCurrentTargetText(newText);
    onTargetTextChange(newText);
  };

  const handleApplyValidationSuggestion = (sourceTerm: string, targetTerm: string) => {
    // Apply terminology suggestion from validation
    const newText = currentTargetText.replace(
      new RegExp(`\\b${sourceTerm}\\b`, 'gi'),
      targetTerm
    );
    setCurrentTargetText(newText);
    onTargetTextChange(newText);
    setHasChanges(newText !== targetText);
  };

  const handleTextSelection = (selectedText: string, startIndex: number, endIndex: number) => {
    setSelectedText(selectedText);
    // You could add AI-powered translation suggestion here
    setSuggestedTranslation(''); // Placeholder for AI suggestion
  };

  const handleTermAdded = (term: any) => {
    console.log('Term added:', term);
    // Refresh terminology or update local state
  };

  const handleUseTMMatch = (match: typeof mockTMMatches[0]) => {
    setCurrentTargetText(match.targetText);
    onTargetTextChange(match.targetText);
  };

  const handleReset = () => {
    setCurrentTargetText(targetText);
    onTargetTextChange(targetText);
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.ctrlKey || e.metaKey) {
      if (e.key === 's') {
        e.preventDefault();
        onSave();
      }
    }
  };

  return (
    <div className="space-y-4">
      {/* Terminology Warning Banner */}
      {projectId && currentTargetText && (
        <TerminologyWarningBanner
          projectId={projectId}
          sourceText={sourceText}
          targetText={currentTargetText}
          targetLanguage={targetLanguage}
          onApplyFix={handleApplyValidationSuggestion}
        />
      )}

      {/* Source Text */}
      <Card>
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <CardTitle className="text-sm font-medium">Source Text</CardTitle>
            <Button
              variant="outline"
              size="sm"
              onClick={handleCopySource}
            >
              <Copy className="mr-2 h-4 w-4" />
              Copy to Target
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          {projectId ? (
            <SelectableText
              text={sourceText}
              onTextSelect={handleTextSelection}
              onQuickAdd={(text) => {
                setSelectedText(text);
                // Open quick add dialog or handle directly
              }}
              onMarkForReview={(text) => {
                setSelectedText(text);
                // Handle mark for review
              }}
            >
              <TerminologyHighlighter
                text={sourceText}
                projectId={projectId}
                targetLanguage={targetLanguage}
                onTermClick={onTermClick}
                onApplyTerm={handleApplyTerm}
              />
            </SelectableText>
          ) : (
            <div className="p-4 bg-gray-50 rounded-lg">
              <p className="text-gray-900 leading-relaxed">{sourceText}</p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Target Text Editor */}
      <Card>
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <CardTitle className="text-sm font-medium">Target Text</CardTitle>
            <div className="flex items-center space-x-2">
              <span className="text-xs text-gray-500">
                {wordCount} words, {charCount} characters
              </span>
              {hasChanges && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleReset}
                >
                  <RotateCcw className="mr-2 h-4 w-4" />
                  Reset
                </Button>
              )}
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <SmartTranslationTextarea
            ref={textareaRef}
            value={currentTargetText}
            onChange={(value) => {
              setCurrentTargetText(value);
              onTargetTextChange(value);
              setHasChanges(value !== targetText);

              // Update word and character counts
              const words = value.trim() ? value.trim().split(/\s+/).length : 0;
              const chars = value.length;
              setWordCount(words);
              setCharCount(chars);
            }}
            onKeyDown={handleKeyDown}
            placeholder="Enter your translation here..."
            className="min-h-[120px] resize-none"
            projectId={projectId}
            targetLanguage={targetLanguage}
          />
          
          {/* Quality Indicators */}
          <div className="flex items-center justify-between mt-3 pt-3 border-t">
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-1">
                <CheckCircle className="h-4 w-4 text-green-600" />
                <span className="text-xs text-green-600">Terminology consistent</span>
              </div>
              <div className="flex items-center space-x-1">
                <AlertTriangle className="h-4 w-4 text-yellow-600" />
                <span className="text-xs text-yellow-600">Length difference: +15%</span>
              </div>
            </div>
            <div className="text-xs text-gray-500">
              Auto-save enabled • Ctrl+S to save manually
            </div>
          </div>
        </CardContent>
      </Card>

      {/* AI Translation Suggestions */}
      <AITranslationSuggestions
        sourceText={sourceText}
        sourceLanguage={sourceLanguage}
        targetLanguage={targetLanguage}
        context={context}
        domain={domain}
        projectId={projectId}
        onSuggestionSelect={(suggestion) => {
          setCurrentTargetText(suggestion);
          onTargetTextChange(suggestion);
        }}
      />

      {/* Terminology Quick Actions */}
      {projectId && (
        <div className="border rounded-lg p-4 bg-gray-50">
          <h4 className="text-sm font-medium mb-3">Terminology Actions</h4>
          <TerminologyQuickActions
            projectId={projectId}
            selectedText={selectedText}
            suggestedTranslation={suggestedTranslation}
            targetLanguage={targetLanguage}
            onTermAdded={handleTermAdded}
          />
        </div>
      )}

      {/* Terminology Validation */}
      {projectId && (
        <TerminologyValidationPanel
          projectId={projectId}
          sourceText={sourceText}
          targetText={currentTargetText}
          targetLanguage={targetLanguage}
          onApplySuggestion={handleApplyValidationSuggestion}
        />
      )}

      {/* Translation Memory Matches */}
      {mockTMMatches.length > 0 && (
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm font-medium flex items-center">
              <Lightbulb className="mr-2 h-4 w-4" />
              Translation Memory Matches
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {mockTMMatches.map((match) => (
                <div
                  key={match.id}
                  className="p-3 border rounded-lg hover:bg-gray-50 cursor-pointer"
                  onClick={() => handleUseTMMatch(match)}
                >
                  <div className="flex items-center justify-between mb-2">
                    <Badge variant="outline">
                      {match.similarity}% match
                    </Badge>
                    <span className="text-xs text-gray-500">{match.origin}</span>
                  </div>
                  <div className="space-y-1">
                    <p className="text-sm text-gray-600">{match.sourceText}</p>
                    <p className="text-sm font-medium">{match.targetText}</p>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Terminology Suggestions */}
      {mockTermSuggestions.length > 0 && (
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm font-medium">Terminology Suggestions</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              {mockTermSuggestions.map((term, index) => (
                <div
                  key={index}
                  className="flex items-center justify-between p-2 bg-blue-50 rounded"
                >
                  <div>
                    <span className="text-sm font-medium">{term.sourceTerm}</span>
                    <span className="mx-2 text-gray-400">→</span>
                    <span className="text-sm">{term.targetTerm}</span>
                  </div>
                  <Badge variant="outline" size="sm">
                    {term.category}
                  </Badge>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
