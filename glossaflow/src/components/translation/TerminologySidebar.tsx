'use client';

import { useState } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  Search, 
  Plus, 
  BookOpen,
  Filter
} from 'lucide-react';

interface TerminologySidebarProps {
  projectId: string;
  sourceText: string;
  onTermSelect: (term: any) => void;
}

// Mock terminology data
const mockTerminology = [
  {
    id: '1',
    sourceTerm: 'mobile application',
    targetTerm: 'モバイルアプリケーション',
    category: 'technical',
    context: 'Software development',
    frequency: 45,
    highlighted: true,
  },
  {
    id: '2',
    sourceTerm: 'task',
    targetTerm: 'タスク',
    category: 'general',
    context: 'Work management',
    frequency: 32,
    highlighted: true,
  },
  {
    id: '3',
    sourceTerm: 'button',
    targetTerm: 'ボタン',
    category: 'technical',
    context: 'User interface',
    frequency: 28,
    highlighted: false,
  },
  {
    id: '4',
    sourceTerm: 'deadline',
    targetTerm: '締切',
    category: 'general',
    context: 'Time management',
    frequency: 15,
    highlighted: false,
  },
  {
    id: '5',
    sourceTerm: 'reminder',
    targetTerm: 'リマインダー',
    category: 'general',
    context: 'Notification system',
    frequency: 12,
    highlighted: false,
  },
];

const categoryColors = {
  technical: 'bg-blue-100 text-blue-800',
  general: 'bg-gray-100 text-gray-800',
  character: 'bg-purple-100 text-purple-800',
  location: 'bg-green-100 text-green-800',
  concept: 'bg-orange-100 text-orange-800',
};

export function TerminologySidebar({ projectId, sourceText, onTermSelect }: TerminologySidebarProps) {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');

  const filteredTerminology = mockTerminology.filter(term => {
    const matchesSearch = term.sourceTerm.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         term.targetTerm.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesCategory = selectedCategory === 'all' || term.category === selectedCategory;
    return matchesSearch && matchesCategory;
  });

  const highlightedTerms = filteredTerminology.filter(term => term.highlighted);
  const otherTerms = filteredTerminology.filter(term => !term.highlighted);

  return (
    <div className="h-full flex flex-col bg-white">
      <div className="p-4 border-b">
        <div className="flex items-center justify-between mb-4">
          <h3 className="font-medium flex items-center">
            <BookOpen className="mr-2 h-4 w-4" />
            Terminology
          </h3>
          <Button size="sm" variant="outline">
            <Plus className="h-4 w-4" />
          </Button>
        </div>

        {/* Search */}
        <div className="relative mb-3">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
          <Input
            placeholder="Search terms..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10"
          />
        </div>

        {/* Category Filter */}
        <div className="flex flex-wrap gap-1">
          <Button
            variant={selectedCategory === 'all' ? 'default' : 'outline'}
            size="sm"
            onClick={() => setSelectedCategory('all')}
          >
            All
          </Button>
          <Button
            variant={selectedCategory === 'technical' ? 'default' : 'outline'}
            size="sm"
            onClick={() => setSelectedCategory('technical')}
          >
            Technical
          </Button>
          <Button
            variant={selectedCategory === 'general' ? 'default' : 'outline'}
            size="sm"
            onClick={() => setSelectedCategory('general')}
          >
            General
          </Button>
        </div>
      </div>

      <div className="flex-1 overflow-y-auto">
        {/* Highlighted Terms */}
        {highlightedTerms.length > 0 && (
          <div className="p-4 border-b bg-yellow-50">
            <h4 className="text-sm font-medium text-yellow-800 mb-3">
              Found in Current Segment
            </h4>
            <div className="space-y-2">
              {highlightedTerms.map((term) => (
                <div
                  key={term.id}
                  className="p-3 bg-white border border-yellow-200 rounded-lg cursor-pointer hover:border-yellow-300 transition-colors"
                  onClick={() => onTermSelect(term)}
                >
                  <div className="flex items-start justify-between mb-2">
                    <div className="flex-1">
                      <div className="font-medium text-sm">{term.sourceTerm}</div>
                      <div className="text-sm text-gray-600">{term.targetTerm}</div>
                    </div>
                    <Badge 
                      size="sm" 
                      className={categoryColors[term.category as keyof typeof categoryColors]}
                    >
                      {term.category}
                    </Badge>
                  </div>
                  {term.context && (
                    <div className="text-xs text-gray-500">{term.context}</div>
                  )}
                </div>
              ))}
            </div>
          </div>
        )}

        {/* All Terms */}
        <div className="p-4">
          <h4 className="text-sm font-medium text-gray-700 mb-3">
            All Terms ({otherTerms.length})
          </h4>
          <div className="space-y-2">
            {otherTerms.map((term) => (
              <div
                key={term.id}
                className="p-3 border rounded-lg cursor-pointer hover:bg-gray-50 transition-colors"
                onClick={() => onTermSelect(term)}
              >
                <div className="flex items-start justify-between mb-2">
                  <div className="flex-1">
                    <div className="font-medium text-sm">{term.sourceTerm}</div>
                    <div className="text-sm text-gray-600">{term.targetTerm}</div>
                  </div>
                  <Badge 
                    size="sm" 
                    className={categoryColors[term.category as keyof typeof categoryColors]}
                  >
                    {term.category}
                  </Badge>
                </div>
                <div className="flex items-center justify-between">
                  {term.context && (
                    <div className="text-xs text-gray-500">{term.context}</div>
                  )}
                  <div className="text-xs text-gray-400">
                    Used {term.frequency} times
                  </div>
                </div>
              </div>
            ))}
          </div>

          {filteredTerminology.length === 0 && (
            <div className="text-center py-8">
              <BookOpen className="h-8 w-8 text-gray-400 mx-auto mb-2" />
              <p className="text-sm text-gray-500">No terms found</p>
              <p className="text-xs text-gray-400">Try adjusting your search</p>
            </div>
          )}
        </div>
      </div>

      {/* Quick Actions */}
      <div className="p-4 border-t bg-gray-50">
        <Button variant="outline" size="sm" className="w-full">
          <Plus className="mr-2 h-4 w-4" />
          Add New Term
        </Button>
      </div>
    </div>
  );
}
