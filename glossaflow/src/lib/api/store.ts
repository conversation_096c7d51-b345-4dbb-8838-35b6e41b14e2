import { configureStore } from '@reduxjs/toolkit';
import { setupListeners } from '@reduxjs/toolkit/query';
import { baseApi } from './base';
import authSlice from '@/store/slices/authSlice';
import uiSlice from '@/store/slices/uiSlice';

// Import the API slices to ensure they're injected into baseApi
import './auth';
import './projects';
import './terminology';
import './term-candidates';
import './users';
import './translation';
import './ai-translation';

export const store = configureStore({
  reducer: {
    auth: authSlice,
    ui: uiSlice,
    [baseApi.reducerPath]: baseApi.reducer,
  },
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: {
        ignoredActions: ['persist/PERSIST', 'persist/REHYDRATE'],
      },
    }).concat(baseApi.middleware),
});

setupListeners(store.dispatch);

export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;
